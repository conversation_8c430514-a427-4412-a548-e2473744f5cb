"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Mail, Send, Users, Plus, X, FileText } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/hooks/use-toast"

interface EmailModalProps {
  isOpen: boolean
  onClose: () => void
  selectedCodes: string[]
}

const emailTemplates = [
  {
    id: "summary",
    name: "Code Summary",
    subject: "Master Codes Summary Report",
    body: "Please find attached the summary of master codes as requested.\n\nThis report includes {count} master codes with their descriptions and current status.\n\nBest regards,\nCodeManager System",
  },
  {
    id: "detailed",
    name: "Detailed Report",
    subject: "Detailed Master Codes Report - {date}",
    body: "Dear Team,\n\nAttached is the detailed master codes report containing {count} codes.\n\nThe report includes:\n- Code identifiers\n- Descriptions\n- Categories\n- Current status\n- Last modification dates\n\nPlease review and let me know if you need any additional information.\n\nBest regards,\n{sender}",
  },
  {
    id: "custom",
    name: "Custom Message",
    subject: "",
    body: "",
  },
]

const predefinedRecipients = [
  { email: "<EMAIL>", name: "Development Team" },
  { email: "<EMAIL>", name: "System Admin" },
  { email: "<EMAIL>", name: "Project Manager" },
]

export default function EmailModal({ isOpen, onClose, selectedCodes }: EmailModalProps) {
  const [recipients, setRecipients] = useState<string[]>([])
  const [newRecipient, setNewRecipient] = useState("")
  const [subject, setSubject] = useState("")
  const [message, setMessage] = useState("")
  const [selectedTemplate, setSelectedTemplate] = useState("summary")
  const [includeAttachment, setIncludeAttachment] = useState(true)
  const [attachmentFormat, setAttachmentFormat] = useState("excel")
  const [isSending, setIsSending] = useState(false)

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId)
    const template = emailTemplates.find((t) => t.id === templateId)
    if (template) {
      setSubject(
        template.subject
          .replace("{count}", selectedCodes.length.toString())
          .replace("{date}", new Date().toLocaleDateString()),
      )
      setMessage(
        template.body
          .replace("{count}", selectedCodes.length.toString())
          .replace("{date}", new Date().toLocaleDateString())
          .replace("{sender}", "CodeManager System"),
      )
    }
  }

  const addRecipient = (email: string) => {
    if (email && !recipients.includes(email)) {
      setRecipients((prev) => [...prev, email])
      setNewRecipient("")
    }
  }

  const removeRecipient = (email: string) => {
    setRecipients((prev) => prev.filter((r) => r !== email))
  }

  const handleSendEmail = async () => {
    if (recipients.length === 0) {
      toast({
        title: "No Recipients",
        description: "Please add at least one recipient.",
        variant: "destructive",
      })
      return
    }

    if (!subject.trim()) {
      toast({
        title: "Subject Required",
        description: "Please enter an email subject.",
        variant: "destructive",
      })
      return
    }

    setIsSending(true)

    // Simulate email sending
    await new Promise((resolve) => setTimeout(resolve, 2500))

    toast({
      title: "Email Sent Successfully!",
      description: `Master codes report sent to ${recipients.length} recipient${recipients.length > 1 ? "s" : ""}.`,
    })

    setIsSending(false)
    onClose()
  }

  // Initialize with first template on open
  useState(() => {
    if (isOpen && selectedTemplate === "summary") {
      handleTemplateChange("summary")
    }
  })

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Mail className="w-5 h-5 text-blue-600" />
            <span>Email Master Codes</span>
          </DialogTitle>
          <DialogDescription>
            Send selected master codes ({selectedCodes.length} codes) via email with optional attachment.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="compose" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="compose">Compose Email</TabsTrigger>
            <TabsTrigger value="attachment">Attachment Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="compose" className="space-y-4 mt-6">
            {/* Recipients */}
            <div className="space-y-3">
              <Label>Recipients</Label>
              <div className="space-y-2">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Enter email address"
                    value={newRecipient}
                    onChange={(e) => setNewRecipient(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault()
                        addRecipient(newRecipient)
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => addRecipient(newRecipient)}
                    disabled={!newRecipient.trim()}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>

                {/* Predefined Recipients */}
                <div className="flex flex-wrap gap-2">
                  {predefinedRecipients.map((recipient) => (
                    <Button
                      key={recipient.email}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addRecipient(recipient.email)}
                      disabled={recipients.includes(recipient.email)}
                      className="text-xs"
                    >
                      <Users className="w-3 h-3 mr-1" />
                      {recipient.name}
                    </Button>
                  ))}
                </div>

                {/* Selected Recipients */}
                {recipients.length > 0 && (
                  <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg">
                    {recipients.map((email) => (
                      <Badge key={email} variant="secondary" className="flex items-center space-x-1">
                        <span>{email}</span>
                        <button
                          onClick={() => removeRecipient(email)}
                          className="ml-1 hover:text-red-600"
                          type="button"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Email Template */}
            <div className="space-y-3">
              <Label>Email Template</Label>
              <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {emailTemplates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Subject */}
            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                placeholder="Enter email subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
              />
            </div>

            {/* Message */}
            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                placeholder="Enter your message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                rows={6}
                className="resize-none"
              />
            </div>
          </TabsContent>

          <TabsContent value="attachment" className="space-y-4 mt-6">
            {/* Include Attachment */}
            <div className="flex items-center space-x-2 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <Checkbox
                id="includeAttachment"
                checked={includeAttachment}
                onCheckedChange={(checked) => setIncludeAttachment(checked as boolean)}
              />
              <div className="flex-1">
                <Label htmlFor="includeAttachment" className="font-medium text-blue-900">
                  Include Master Codes as Attachment
                </Label>
                <p className="text-sm text-blue-700 mt-1">Attach the selected master codes as a file to the email</p>
              </div>
            </div>

            {includeAttachment && (
              <>
                {/* Attachment Format */}
                <div className="space-y-3">
                  <Label>Attachment Format</Label>
                  <Select value={attachmentFormat} onValueChange={setAttachmentFormat}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                      <SelectItem value="csv">CSV (.csv)</SelectItem>
                      <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Attachment Preview */}
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <div className="flex items-center space-x-2 mb-2">
                    <FileText className="w-5 h-5 text-gray-600" />
                    <h4 className="font-medium text-gray-900">Attachment Preview</h4>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>
                      <strong>Filename:</strong> master-codes-{new Date().toISOString().split("T")[0]}.
                      {attachmentFormat === "excel" ? "xlsx" : attachmentFormat}
                    </p>
                    <p>
                      <strong>Records:</strong> {selectedCodes.length} master codes
                    </p>
                    <p>
                      <strong>Fields:</strong> Code, Description, Category, Status, Last Modified
                    </p>
                  </div>
                </div>
              </>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSendEmail} disabled={isSending} className="bg-blue-600 hover:bg-blue-700">
            {isSending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Sending...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                Send Email
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
