import { Card, CardContent } from "@/components/ui/card"
import { TrendingUp, TrendingDown, FileText, Users, Mail, Download } from "lucide-react"

export default function StatsCards() {
  const stats = [
    {
      title: "Total Master Codes",
      value: "2,847",
      change: "+12%",
      trend: "up",
      icon: FileText,
      color: "blue",
    },
    {
      title: "Active Mappings",
      value: "1,234",
      change: "+8%",
      trend: "up",
      icon: Users,
      color: "green",
    },
    {
      title: "Emails Sent",
      value: "456",
      change: "-3%",
      trend: "down",
      icon: Mail,
      color: "purple",
    },
    {
      title: "Exports Today",
      value: "89",
      change: "+15%",
      trend: "up",
      icon: Download,
      color: "orange",
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        const TrendIcon = stat.trend === "up" ? TrendingUp : TrendingDown

        return (
          <Card key={index} className="border-0 shadow-sm bg-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <div className="flex items-center space-x-1">
                    <TrendIcon className={`w-4 h-4 ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`} />
                    <span className={`text-sm font-medium ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500">vs last month</span>
                  </div>
                </div>
                <div
                  className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                    stat.color === "blue"
                      ? "bg-blue-100"
                      : stat.color === "green"
                        ? "bg-green-100"
                        : stat.color === "purple"
                          ? "bg-purple-100"
                          : "bg-orange-100"
                  }`}
                >
                  <Icon
                    className={`w-6 h-6 ${
                      stat.color === "blue"
                        ? "text-blue-600"
                        : stat.color === "green"
                          ? "text-green-600"
                          : stat.color === "purple"
                            ? "text-purple-600"
                            : "text-orange-600"
                    }`}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
