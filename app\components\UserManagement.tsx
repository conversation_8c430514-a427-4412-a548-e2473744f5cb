"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Edit3, Trash2, Search, Users, Shield, UserCheck, UserX, Crown, Eye } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import UserModal from "./UserModal"
import RolePermissionsCard from "./RolePermissionsCard"

interface UserManagementProps {
  onNavigate: (page: string) => void
}

interface User {
  id: string
  name: string
  email: string
  role: "admin" | "editor" | "viewer"
  status: "active" | "inactive" | "pending"
  lastLogin: string
  createdAt: string
  department: string
  permissions: {
    canManageMappings: boolean
    canGenerateCodes: boolean
    canViewDashboard: boolean
    canManageUsers: boolean
    canExportData: boolean
    canImportData: boolean
  }
}

const initialUsers: User[] = [
  {
    id: "user_1",
    name: "John Smith",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    lastLogin: "2024-01-20",
    createdAt: "2024-01-01",
    department: "IT Administration",
    permissions: {
      canManageMappings: true,
      canGenerateCodes: true,
      canViewDashboard: true,
      canManageUsers: true,
      canExportData: true,
      canImportData: true,
    },
  },
  {
    id: "user_2",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    role: "editor",
    status: "active",
    lastLogin: "2024-01-19",
    createdAt: "2024-01-05",
    department: "Development",
    permissions: {
      canManageMappings: true,
      canGenerateCodes: true,
      canViewDashboard: true,
      canManageUsers: false,
      canExportData: true,
      canImportData: false,
    },
  },
  {
    id: "user_3",
    name: "Mike Davis",
    email: "<EMAIL>",
    role: "viewer",
    status: "active",
    lastLogin: "2024-01-18",
    createdAt: "2024-01-10",
    department: "Business Analysis",
    permissions: {
      canManageMappings: false,
      canGenerateCodes: false,
      canViewDashboard: true,
      canManageUsers: false,
      canExportData: false,
      canImportData: false,
    },
  },
  {
    id: "user_4",
    name: "Emily Chen",
    email: "<EMAIL>",
    role: "editor",
    status: "pending",
    lastLogin: "Never",
    createdAt: "2024-01-15",
    department: "Quality Assurance",
    permissions: {
      canManageMappings: false,
      canGenerateCodes: true,
      canViewDashboard: true,
      canManageUsers: false,
      canExportData: true,
      canImportData: false,
    },
  },
  {
    id: "user_5",
    name: "David Wilson",
    email: "<EMAIL>",
    role: "viewer",
    status: "inactive",
    lastLogin: "2024-01-10",
    createdAt: "2023-12-20",
    department: "Operations",
    permissions: {
      canManageMappings: false,
      canGenerateCodes: false,
      canViewDashboard: true,
      canManageUsers: false,
      canExportData: false,
      canImportData: false,
    },
  },
]

export default function UserManagement({ onNavigate }: UserManagementProps) {
  const [users, setUsers] = useState<User[]>(initialUsers)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedRole, setSelectedRole] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [showModal, setShowModal] = useState(false)
  const [modalType, setModalType] = useState<"add" | "edit">("add")
  const [selectedUser, setSelectedUser] = useState<User | null>(null)

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.department.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesRole = selectedRole === "all" || user.role === selectedRole
    const matchesStatus = selectedStatus === "all" || user.status === selectedStatus

    return matchesSearch && matchesRole && matchesStatus
  })

  const handleAddUser = () => {
    setModalType("add")
    setSelectedUser(null)
    setShowModal(true)
  }

  const handleEditUser = (user: User) => {
    setModalType("edit")
    setSelectedUser(user)
    setShowModal(true)
  }

  const handleDeleteUser = (userId: string) => {
    setUsers((prev) => prev.filter((user) => user.id !== userId))
    toast({
      title: "User Deleted",
      description: "The user has been successfully removed from the system.",
    })
  }

  const handleToggleStatus = (userId: string) => {
    setUsers((prev) =>
      prev.map((user) =>
        user.id === userId
          ? {
              ...user,
              status: user.status === "active" ? "inactive" : "active",
            }
          : user,
      ),
    )

    const user = users.find((u) => u.id === userId)
    toast({
      title: "Status Updated",
      description: `${user?.name} has been ${user?.status === "active" ? "deactivated" : "activated"}.`,
    })
  }

  const handleSaveUser = (userData: Omit<User, "id" | "createdAt" | "lastLogin">) => {
    const now = new Date().toISOString().split("T")[0]

    if (modalType === "add") {
      const newUser: User = {
        ...userData,
        id: `user_${Date.now()}`,
        createdAt: now,
        lastLogin: "Never",
      }

      setUsers((prev) => [...prev, newUser])
      toast({
        title: "User Added",
        description: "New user has been successfully added to the system.",
      })
    } else if (selectedUser) {
      setUsers((prev) =>
        prev.map((user) =>
          user.id === selectedUser.id
            ? {
                ...user,
                ...userData,
              }
            : user,
        ),
      )

      toast({
        title: "User Updated",
        description: "User information has been successfully updated.",
      })
    }

    setShowModal(false)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin":
        return <Crown className="w-4 h-4" />
      case "editor":
        return <Edit3 className="w-4 h-4" />
      case "viewer":
        return <Eye className="w-4 h-4" />
      default:
        return <Users className="w-4 h-4" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-purple-100 text-purple-800 border-purple-200"
      case "editor":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "viewer":
        return "bg-gray-100 text-gray-800 border-gray-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200"
      case "inactive":
        return "bg-red-100 text-red-800 border-red-200"
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getStats = () => {
    return {
      total: users.length,
      active: users.filter((u) => u.status === "active").length,
      admins: users.filter((u) => u.role === "admin").length,
      editors: users.filter((u) => u.role === "editor").length,
      viewers: users.filter((u) => u.role === "viewer").length,
    }
  }

  const stats = getStats()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">User Management</h1>
                  <p className="text-sm text-gray-600">Manage user access and permissions</p>
                </div>
              </div>
            </div>
            <Button
              onClick={handleAddUser}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add User
            </Button>
          </div>
        </div>
      </header>

      <div className="p-6 max-w-7xl mx-auto space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active</p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <UserCheck className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Admins</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.admins}</p>
                </div>
                <Crown className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Editors</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.editors}</p>
                </div>
                <Edit3 className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Viewers</p>
                  <p className="text-2xl font-bold text-gray-600">{stats.viewers}</p>
                </div>
                <Eye className="w-8 h-8 text-gray-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="users" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 bg-white border border-gray-200">
            <TabsTrigger value="users" className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>User Management</span>
            </TabsTrigger>
            <TabsTrigger value="roles" className="flex items-center space-x-2">
              <Shield className="w-4 h-4" />
              <span>Role Permissions</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                  <div className="flex flex-1 items-center space-x-4">
                    <div className="relative flex-1 max-w-md">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <select
                      value={selectedRole}
                      onChange={(e) => setSelectedRole(e.target.value)}
                      className="px-3 py-2 border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="all">All Roles</option>
                      <option value="admin">Admin</option>
                      <option value="editor">Editor</option>
                      <option value="viewer">Viewer</option>
                    </select>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="px-3 py-2 border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="pending">Pending</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Users Table */}
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="text-left p-4 font-medium text-gray-900">User</th>
                        <th className="text-left p-4 font-medium text-gray-900">Role</th>
                        <th className="text-left p-4 font-medium text-gray-900">Status</th>
                        <th className="text-left p-4 font-medium text-gray-900">Department</th>
                        <th className="text-left p-4 font-medium text-gray-900">Last Login</th>
                        <th className="text-left p-4 font-medium text-gray-900">Permissions</th>
                        <th className="w-32 p-4 font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {filteredUsers.map((user) => (
                        <tr key={user.id} className="hover:bg-gray-50">
                          <td className="p-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                                {user.name.charAt(0)}
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{user.name}</p>
                                <p className="text-sm text-gray-600">{user.email}</p>
                              </div>
                            </div>
                          </td>
                          <td className="p-4">
                            <Badge className={`${getRoleColor(user.role)} flex items-center space-x-1 w-fit`}>
                              {getRoleIcon(user.role)}
                              <span className="capitalize">{user.role}</span>
                            </Badge>
                          </td>
                          <td className="p-4">
                            <Badge className={getStatusColor(user.status)}>
                              <span className="capitalize">{user.status}</span>
                            </Badge>
                          </td>
                          <td className="p-4">
                            <span className="text-gray-700">{user.department}</span>
                          </td>
                          <td className="p-4">
                            <span className="text-gray-600">{user.lastLogin}</span>
                          </td>
                          <td className="p-4">
                            <div className="flex flex-wrap gap-1">
                              {user.permissions.canManageMappings && (
                                <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                  Mappings
                                </Badge>
                              )}
                              {user.permissions.canGenerateCodes && (
                                <Badge
                                  variant="outline"
                                  className="text-xs bg-green-50 text-green-700 border-green-200"
                                >
                                  Generate
                                </Badge>
                              )}
                              {user.permissions.canManageUsers && (
                                <Badge
                                  variant="outline"
                                  className="text-xs bg-purple-50 text-purple-700 border-purple-200"
                                >
                                  Users
                                </Badge>
                              )}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleToggleStatus(user.id)}
                                className={
                                  user.status === "active"
                                    ? "border-orange-200 text-orange-700 hover:bg-orange-50"
                                    : "border-green-200 text-green-700 hover:bg-green-50"
                                }
                              >
                                {user.status === "active" ? (
                                  <UserX className="w-4 h-4" />
                                ) : (
                                  <UserCheck className="w-4 h-4" />
                                )}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditUser(user)}
                                className="border-blue-200 text-blue-700 hover:bg-blue-50"
                              >
                                <Edit3 className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteUser(user.id)}
                                className="border-red-200 text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {filteredUsers.length === 0 && (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500">No users found matching your criteria.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="roles" className="space-y-6">
            <RolePermissionsCard />
          </TabsContent>
        </Tabs>
      </div>

      {/* User Modal */}
      <UserModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        type={modalType}
        user={selectedUser}
        onSave={handleSaveUser}
      />
    </div>
  )
}
