"use client"

import { useState } from "react"
import SignInPage from "./components/SignInPage"
import AppLayout from "./components/AppLayout"

export default function Home() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      {!isAuthenticated ? (
        <SignInPage onSignIn={() => setIsAuthenticated(true)} />
      ) : (
        <AppLayout onSignOut={() => setIsAuthenticated(false)} />
      )}
    </div>
  )
}
