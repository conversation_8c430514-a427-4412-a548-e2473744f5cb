"use client"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Edit3, Trash2, MoreH<PERSON>zon<PERSON> } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface CodeTableProps {
  searchTerm: string
  selectedCodes: string[]
  onSelectionChange: (codes: string[]) => void
  onEdit: () => void
}

const mockCodes = [
  {
    id: "MC001",
    code: "USER_AUTH",
    description: "User authentication and authorization codes",
    category: "Security",
    status: "Active",
    lastModified: "2024-01-15",
    mappings: 12,
  },
  {
    id: "MC002",
    code: "DATA_PROC",
    description: "Data processing and validation routines",
    category: "Processing",
    status: "Active",
    lastModified: "2024-01-14",
    mappings: 8,
  },
  {
    id: "MC003",
    code: "EMAIL_SVC",
    description: "Email service integration codes",
    category: "Communication",
    status: "Pending",
    lastModified: "2024-01-13",
    mappings: 5,
  },
  {
    id: "MC004",
    code: "REPORT_GEN",
    description: "Report generation and export functionality",
    category: "Reporting",
    status: "Active",
    lastModified: "2024-01-12",
    mappings: 15,
  },
  {
    id: "MC005",
    code: "API_INTEG",
    description: "Third-party API integration codes",
    category: "Integration",
    status: "Inactive",
    lastModified: "2024-01-11",
    mappings: 3,
  },
]

export default function CodeTable({ searchTerm, selectedCodes, onSelectionChange, onEdit }: CodeTableProps) {
  const filteredCodes = mockCodes.filter(
    (code) =>
      code.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      code.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      code.category.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(filteredCodes.map((code) => code.id))
    } else {
      onSelectionChange([])
    }
  }

  const handleSelectCode = (codeId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedCodes, codeId])
    } else {
      onSelectionChange(selectedCodes.filter((id) => id !== codeId))
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 border-green-200"
      case "Pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "Inactive":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="w-12 p-4">
                  <Checkbox
                    checked={selectedCodes.length === filteredCodes.length && filteredCodes.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </th>
                <th className="text-left p-4 font-medium text-gray-900">Code</th>
                <th className="text-left p-4 font-medium text-gray-900">Description</th>
                <th className="text-left p-4 font-medium text-gray-900">Category</th>
                <th className="text-left p-4 font-medium text-gray-900">Status</th>
                <th className="text-left p-4 font-medium text-gray-900">Mappings</th>
                <th className="text-left p-4 font-medium text-gray-900">Last Modified</th>
                <th className="w-12 p-4"></th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredCodes.map((code) => (
                <tr key={code.id} className="hover:bg-gray-50">
                  <td className="p-4">
                    <Checkbox
                      checked={selectedCodes.includes(code.id)}
                      onCheckedChange={(checked) => handleSelectCode(code.id, checked as boolean)}
                    />
                  </td>
                  <td className="p-4">
                    <div className="font-medium text-gray-900">{code.code}</div>
                    <div className="text-sm text-gray-500">{code.id}</div>
                  </td>
                  <td className="p-4">
                    <div className="text-gray-900 max-w-xs truncate">{code.description}</div>
                  </td>
                  <td className="p-4">
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      {code.category}
                    </Badge>
                  </td>
                  <td className="p-4">
                    <Badge className={getStatusColor(code.status)}>{code.status}</Badge>
                  </td>
                  <td className="p-4">
                    <span className="text-gray-900">{code.mappings}</span>
                  </td>
                  <td className="p-4">
                    <span className="text-gray-600">{code.lastModified}</span>
                  </td>
                  <td className="p-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={onEdit}>
                          <Edit3 className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {filteredCodes.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No codes found matching your search.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
