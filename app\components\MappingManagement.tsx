"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Plus, Edit3, Trash2, Search, Settings, Code, CheckCircle, AlertCircle, Layers } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import MappingOptionModal from "./MappingOptionModal"

interface MappingManagementProps {
  onNavigate: (page: string) => void
}

interface MappingOption {
  id: string
  code: string
  description: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface MappingCategory {
  id: string
  label: string
  description: string
  options: MappingOption[]
}

// Initial mapping data
const initialMappingData: MappingCategory[] = [
  {
    id: "category",
    label: "Category",
    description: "Main functional categories for code classification",
    options: [
      {
        id: "cat_1",
        code: "SECURITY",
        description: "Security and authentication related codes",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "cat_2",
        code: "PROCESSING",
        description: "Data processing and computation codes",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "cat_3",
        code: "COMMUNICATION",
        description: "Communication and messaging codes",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "module",
    label: "Module",
    description: "System modules and components",
    options: [
      {
        id: "mod_1",
        code: "USER_MGMT",
        description: "User Management module",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "mod_2",
        code: "DATA_PROC",
        description: "Data Processing module",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "operation",
    label: "Operation Type",
    description: "Types of operations performed",
    options: [
      {
        id: "op_1",
        code: "CREATE",
        description: "Create new records or entities",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "op_2",
        code: "READ",
        description: "Read or retrieve data",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "priority",
    label: "Priority Level",
    description: "Priority levels for code execution",
    options: [
      {
        id: "pri_1",
        code: "HIGH",
        description: "High priority operations",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "pri_2",
        code: "MEDIUM",
        description: "Medium priority operations",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "environment",
    label: "Environment",
    description: "Deployment environments",
    options: [
      {
        id: "env_1",
        code: "PROD",
        description: "Production environment",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "env_2",
        code: "DEV",
        description: "Development environment",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "version",
    label: "Version",
    description: "Version control identifiers",
    options: [
      {
        id: "ver_1",
        code: "V1",
        description: "Version 1.0",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "ver_2",
        code: "V2",
        description: "Version 2.0",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "region",
    label: "Region",
    description: "Geographic regions and locations",
    options: [
      {
        id: "reg_1",
        code: "US",
        description: "United States region",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "reg_2",
        code: "EU",
        description: "European Union region",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "protocol",
    label: "Protocol",
    description: "Communication protocols",
    options: [
      {
        id: "prot_1",
        code: "HTTP",
        description: "HyperText Transfer Protocol",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "prot_2",
        code: "HTTPS",
        description: "Secure HyperText Transfer Protocol",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "format",
    label: "Data Format",
    description: "Data formats and structures",
    options: [
      {
        id: "fmt_1",
        code: "JSON",
        description: "JavaScript Object Notation",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "fmt_2",
        code: "XML",
        description: "eXtensible Markup Language",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
  {
    id: "status",
    label: "Initial Status",
    description: "Initial status for new codes",
    options: [
      {
        id: "stat_1",
        code: "ACTIVE",
        description: "Active and ready for use",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
      {
        id: "stat_2",
        code: "PENDING",
        description: "Pending approval or review",
        isActive: true,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-15",
      },
    ],
  },
]

export default function MappingManagement({ onNavigate }: MappingManagementProps) {
  const [mappingData, setMappingData] = useState<MappingCategory[]>(initialMappingData)
  const [selectedCategory, setSelectedCategory] = useState<string>("category")
  const [searchTerm, setSearchTerm] = useState("")
  const [showModal, setShowModal] = useState(false)
  const [modalType, setModalType] = useState<"add" | "edit">("add")
  const [selectedOption, setSelectedOption] = useState<MappingOption | null>(null)

  const currentCategory = mappingData.find((cat) => cat.id === selectedCategory)
  const filteredOptions = currentCategory?.options.filter(
    (option) =>
      option.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      option.description.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddOption = () => {
    setModalType("add")
    setSelectedOption(null)
    setShowModal(true)
  }

  const handleEditOption = (option: MappingOption) => {
    setModalType("edit")
    setSelectedOption(option)
    setShowModal(true)
  }

  const handleDeleteOption = (optionId: string) => {
    setMappingData((prev) =>
      prev.map((category) =>
        category.id === selectedCategory
          ? {
              ...category,
              options: category.options.filter((option) => option.id !== optionId),
            }
          : category,
      ),
    )

    toast({
      title: "Option Deleted",
      description: "The mapping option has been successfully deleted.",
    })
  }

  const handleToggleStatus = (optionId: string) => {
    setMappingData((prev) =>
      prev.map((category) =>
        category.id === selectedCategory
          ? {
              ...category,
              options: category.options.map((option) =>
                option.id === optionId
                  ? {
                      ...option,
                      isActive: !option.isActive,
                      updatedAt: new Date().toISOString().split("T")[0],
                    }
                  : option,
              ),
            }
          : category,
      ),
    )

    toast({
      title: "Status Updated",
      description: "The option status has been updated successfully.",
    })
  }

  const handleSaveOption = (optionData: Omit<MappingOption, "id" | "createdAt" | "updatedAt">) => {
    const now = new Date().toISOString().split("T")[0]

    if (modalType === "add") {
      const newOption: MappingOption = {
        ...optionData,
        id: `${selectedCategory}_${Date.now()}`,
        createdAt: now,
        updatedAt: now,
      }

      setMappingData((prev) =>
        prev.map((category) =>
          category.id === selectedCategory
            ? {
                ...category,
                options: [...category.options, newOption],
              }
            : category,
        ),
      )

      toast({
        title: "Option Added",
        description: "New mapping option has been successfully added.",
      })
    } else if (selectedOption) {
      setMappingData((prev) =>
        prev.map((category) =>
          category.id === selectedCategory
            ? {
                ...category,
                options: category.options.map((option) =>
                  option.id === selectedOption.id
                    ? {
                        ...option,
                        ...optionData,
                        updatedAt: now,
                      }
                    : option,
                ),
              }
            : category,
        ),
      )

      toast({
        title: "Option Updated",
        description: "Mapping option has been successfully updated.",
      })
    }

    setShowModal(false)
  }

  const getTotalOptions = () => {
    return mappingData.reduce((total, category) => total + category.options.length, 0)
  }

  const getActiveOptions = () => {
    return mappingData.reduce((total, category) => total + category.options.filter((opt) => opt.isActive).length, 0)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-green-600 to-teal-600 rounded-xl flex items-center justify-center">
                  <Layers className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Mapping Management</h1>
                  <p className="text-sm text-gray-600">Configure master code generation options</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-600">Total Options</p>
                <p className="text-lg font-bold text-gray-900">{getTotalOptions()}</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Active</p>
                <p className="text-lg font-bold text-green-600">{getActiveOptions()}</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="p-6 max-w-7xl mx-auto space-y-6">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-indigo-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Categories</p>
                  <p className="text-2xl font-bold text-blue-900">{mappingData.length}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Layers className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-emerald-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Total Options</p>
                  <p className="text-2xl font-bold text-green-900">{getTotalOptions()}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <Code className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-pink-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Active Options</p>
                  <p className="text-2xl font-bold text-purple-900">{getActiveOptions()}</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Category Sidebar */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="text-lg">Categories</CardTitle>
              <CardDescription>Select a category to manage its options</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-1">
                {mappingData.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full text-left p-4 rounded-lg transition-all duration-200 ${
                      selectedCategory === category.id
                        ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md"
                        : "hover:bg-gray-50 text-gray-700"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{category.label}</p>
                        <p
                          className={`text-xs mt-1 ${
                            selectedCategory === category.id ? "text-blue-100" : "text-gray-500"
                          }`}
                        >
                          {category.options.length} options
                        </p>
                      </div>
                      <Badge
                        variant={selectedCategory === category.id ? "secondary" : "outline"}
                        className={
                          selectedCategory === category.id
                            ? "bg-white/20 text-white border-white/30"
                            : "bg-blue-50 text-blue-700 border-blue-200"
                        }
                      >
                        {category.options.filter((opt) => opt.isActive).length}
                      </Badge>
                    </div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Options Management */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="w-5 h-5 text-blue-600" />
                    <span>{currentCategory?.label} Options</span>
                  </CardTitle>
                  <CardDescription>{currentCategory?.description}</CardDescription>
                </div>
                <Button
                  onClick={handleAddOption}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Option
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search options..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              {/* Options List */}
              <div className="space-y-3">
                {filteredOptions?.map((option) => (
                  <div
                    key={option.id}
                    className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-shadow"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={option.isActive ? "default" : "secondary"}
                            className={
                              option.isActive
                                ? "bg-green-100 text-green-800 border-green-200"
                                : "bg-gray-100 text-gray-600 border-gray-200"
                            }
                          >
                            {option.isActive ? (
                              <CheckCircle className="w-3 h-3 mr-1" />
                            ) : (
                              <AlertCircle className="w-3 h-3 mr-1" />
                            )}
                            {option.isActive ? "Active" : "Inactive"}
                          </Badge>
                          <code className="px-2 py-1 bg-gray-100 rounded text-sm font-mono text-gray-800">
                            {option.code}
                          </code>
                        </div>
                      </div>
                      <p className="text-gray-700 mt-1">{option.description}</p>
                      <p className="text-xs text-gray-500 mt-2">
                        Created: {option.createdAt} • Updated: {option.updatedAt}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleToggleStatus(option.id)}
                        className={
                          option.isActive
                            ? "border-orange-200 text-orange-700 hover:bg-orange-50"
                            : "border-green-200 text-green-700 hover:bg-green-50"
                        }
                      >
                        {option.isActive ? "Deactivate" : "Activate"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditOption(option)}
                        className="border-blue-200 text-blue-700 hover:bg-blue-50"
                      >
                        <Edit3 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteOption(option.id)}
                        className="border-red-200 text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                {filteredOptions?.length === 0 && (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Search className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500">No options found matching your search.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modal */}
      <MappingOptionModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        type={modalType}
        option={selectedOption}
        categoryLabel={currentCategory?.label || ""}
        onSave={handleSaveOption}
      />
    </div>
  )
}
