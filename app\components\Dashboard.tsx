"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Search, Filter, Download, Mail, Upload, BarChart3, TrendingUp, Users, FileText } from "lucide-react"
import StatsCards from "./StatsCards"
import CodeTable from "./CodeTable"
import ChartsSection from "./ChartsSection"
import CodeManagementModal from "./CodeManagementModal"
import { SidebarTrigger } from "@/components/Sidebar"
import { Separator } from "@/components/ui/separator"
import ExportModal from "./ExportModal"
import EmailModal from "./EmailModal"

type DashboardProps = {}

export default function Dashboard() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCodes, setSelectedCodes] = useState<string[]>([])
  const [showModal, setShowModal] = useState(false)
  const [modalType, setModalType] = useState<"edit" | "import">("edit")
  const [showExportModal, setShowExportModal] = useState(false)
  const [showEmailModal, setShowEmailModal] = useState(false)

  const handleAction = (action: string) => {
    switch (action) {
      case "edit":
        setModalType("edit")
        setShowModal(true)
        break
      case "import":
        setModalType("import")
        setShowModal(true)
        break
      case "email":
        if (selectedCodes.length === 0) {
          // Show toast or alert
          return
        }
        setShowEmailModal(true)
        break
      case "export":
        setShowExportModal(true)
        break
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6 space-y-6">
        {/* Add header with sidebar trigger */}
        <div className="flex items-center space-x-4 mb-6">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Overview of your code management system</p>
          </div>
        </div>

        {/* Rest of the dashboard content remains the same */}
        <StatsCards />

        {/* Main Content */}
        <Tabs defaultValue="dashboard" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-white border border-gray-200">
            <TabsTrigger value="dashboard" className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4" />
              <span>Dashboard</span>
            </TabsTrigger>
            <TabsTrigger value="codes" className="flex items-center space-x-2">
              <FileText className="w-4 h-4" />
              <span>Master Codes</span>
            </TabsTrigger>
            <TabsTrigger value="mappings" className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4" />
              <span>Mappings</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>Analytics</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <ChartsSection />
          </TabsContent>

          <TabsContent value="codes" className="space-y-6">
            {/* Action Bar */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                  <div className="flex flex-1 items-center space-x-4">
                    <div className="relative flex-1 max-w-md">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder="Search codes and descriptions..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <Button variant="outline" size="sm" className="flex items-center space-x-2">
                      <Filter className="w-4 h-4" />
                      <span>Filter</span>
                    </Button>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAction("email")}
                      disabled={selectedCodes.length === 0}
                      className="flex items-center space-x-2"
                    >
                      <Mail className="w-4 h-4" />
                      <span>Email Selected ({selectedCodes.length})</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAction("export")}
                      className="flex items-center space-x-2"
                    >
                      <Download className="w-4 h-4" />
                      <span>Export</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAction("import")}
                      className="flex items-center space-x-2"
                    >
                      <Upload className="w-4 h-4" />
                      <span>Import</span>
                    </Button>
                  </div>
                </div>
                {selectedCodes.length > 0 && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="text-sm text-blue-800">
                      <span className="font-medium">{selectedCodes.length}</span> codes selected
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Codes Table */}
            <CodeTable
              searchTerm={searchTerm}
              selectedCodes={selectedCodes}
              onSelectionChange={setSelectedCodes}
              onEdit={() => handleAction("edit")}
            />
          </TabsContent>

          <TabsContent value="mappings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Code Mappings</CardTitle>
                <CardDescription>Manage relationships between master codes and their mappings</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Mapping management interface will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Usage Analytics</CardTitle>
                <CardDescription>View detailed analytics and usage patterns</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Analytics dashboard will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Modal */}
      <CodeManagementModal isOpen={showModal} onClose={() => setShowModal(false)} type={modalType} />

      {/* Export Modal */}
      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        selectedCodes={selectedCodes}
        totalCodes={5} // This would be the actual total from your data
      />

      {/* Email Modal */}
      <EmailModal isOpen={showEmailModal} onClose={() => setShowEmailModal(false)} selectedCodes={selectedCodes} />
    </div>
  )
}
