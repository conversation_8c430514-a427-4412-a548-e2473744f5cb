"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Download, FileSpreadsheet, FileText, Database } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface ExportModalProps {
  isOpen: boolean
  onClose: () => void
  selectedCodes: string[]
  totalCodes: number
}

const exportFormats = [
  {
    value: "excel",
    label: "Excel (.xlsx)",
    icon: FileSpreadsheet,
    description: "Spreadsheet format with multiple sheets",
  },
  {
    value: "csv",
    label: "CSV (.csv)",
    icon: FileText,
    description: "Comma-separated values format",
  },
  {
    value: "json",
    label: "JSO<PERSON> (.json)",
    icon: Database,
    description: "JavaScript Object Notation format",
  },
]

const exportFields = [
  { id: "code", label: "Master Code", required: true },
  { id: "description", label: "Description", required: true },
  { id: "category", label: "Category", required: false },
  { id: "status", label: "Status", required: false },
  { id: "mappings", label: "Mapping Count", required: false },
  { id: "lastModified", label: "Last Modified", required: false },
  { id: "createdDate", label: "Created Date", required: false },
]

export default function ExportModal({ isOpen, onClose, selectedCodes, totalCodes }: ExportModalProps) {
  const [exportFormat, setExportFormat] = useState("excel")
  const [exportScope, setExportScope] = useState<"selected" | "all">("selected")
  const [selectedFields, setSelectedFields] = useState<string[]>(["code", "description", "category", "status"])
  const [isExporting, setIsExporting] = useState(false)

  const handleFieldToggle = (fieldId: string, checked: boolean) => {
    const field = exportFields.find((f) => f.id === fieldId)
    if (field?.required) return // Don't allow unchecking required fields

    if (checked) {
      setSelectedFields((prev) => [...prev, fieldId])
    } else {
      setSelectedFields((prev) => prev.filter((id) => id !== fieldId))
    }
  }

  const handleExport = async () => {
    setIsExporting(true)

    // Simulate export process
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const selectedFormat = exportFormats.find((f) => f.value === exportFormat)
    const itemCount = exportScope === "selected" ? selectedCodes.length : totalCodes

    // Create mock file content
    const timestamp = new Date().toISOString().split("T")[0]
    const filename = `master-codes-${timestamp}.${exportFormat === "excel" ? "xlsx" : exportFormat}`

    // In a real app, this would generate and download the actual file
    const mockContent = `Mock export data for ${itemCount} codes in ${selectedFormat?.label} format`
    const blob = new Blob([mockContent], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "Export Complete!",
      description: `Successfully exported ${itemCount} master codes as ${selectedFormat?.label}`,
    })

    setIsExporting(false)
    onClose()
  }

  const selectedFormat = exportFormats.find((f) => f.value === exportFormat)
  const SelectedIcon = selectedFormat?.icon || Download

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Download className="w-5 h-5 text-blue-600" />
            <span>Export Master Codes</span>
          </DialogTitle>
          <DialogDescription>
            Export your master codes in various formats with customizable field selection.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Scope */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Scope</Label>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => setExportScope("selected")}
                className={`p-4 rounded-lg border-2 transition-all ${
                  exportScope === "selected"
                    ? "border-blue-500 bg-blue-50 text-blue-900"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                disabled={selectedCodes.length === 0}
              >
                <div className="text-center">
                  <p className="font-medium">Selected Codes</p>
                  <p className="text-sm text-gray-600 mt-1">
                    {selectedCodes.length} {selectedCodes.length === 1 ? "code" : "codes"}
                  </p>
                </div>
              </button>
              <button
                onClick={() => setExportScope("all")}
                className={`p-4 rounded-lg border-2 transition-all ${
                  exportScope === "all"
                    ? "border-blue-500 bg-blue-50 text-blue-900"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <div className="text-center">
                  <p className="font-medium">All Codes</p>
                  <p className="text-sm text-gray-600 mt-1">
                    {totalCodes} {totalCodes === 1 ? "code" : "codes"}
                  </p>
                </div>
              </button>
            </div>
            {selectedCodes.length === 0 && (
              <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
                No codes selected. Please select codes or choose "All Codes" option.
              </p>
            )}
          </div>

          {/* Export Format */}
          <div className="space-y-3">
            <Label htmlFor="format">Export Format</Label>
            <Select value={exportFormat} onValueChange={setExportFormat}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {exportFormats.map((format) => {
                  const Icon = format.icon
                  return (
                    <SelectItem key={format.value} value={format.value}>
                      <div className="flex items-center space-x-2">
                        <Icon className="w-4 h-4" />
                        <div>
                          <p className="font-medium">{format.label}</p>
                          <p className="text-xs text-gray-500">{format.description}</p>
                        </div>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Field Selection */}
          <div className="space-y-3">
            <Label>Fields to Include</Label>
            <div className="grid grid-cols-2 gap-3">
              {exportFields.map((field) => (
                <div key={field.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={field.id}
                    checked={selectedFields.includes(field.id)}
                    onCheckedChange={(checked) => handleFieldToggle(field.id, checked as boolean)}
                    disabled={field.required}
                  />
                  <Label htmlFor={field.id} className="text-sm">
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">* Required fields cannot be deselected</p>
          </div>

          {/* Preview */}
          <div className="bg-gray-50 p-4 rounded-lg border">
            <div className="flex items-center space-x-2 mb-3">
              <SelectedIcon className="w-5 h-5 text-blue-600" />
              <h4 className="font-medium text-gray-900">Export Preview</h4>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Format:</span>
                <span className="font-medium">{selectedFormat?.label}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Records:</span>
                <span className="font-medium">
                  {exportScope === "selected" ? selectedCodes.length : totalCodes} codes
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Fields:</span>
                <span className="font-medium">{selectedFields.length} selected</span>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || (exportScope === "selected" && selectedCodes.length === 0)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Export {exportScope === "selected" ? selectedCodes.length : totalCodes} Codes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
