"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Plus, Edit3, Code, FileText } from "lucide-react"

interface MappingOption {
  id: string
  code: string
  description: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface MappingOptionModalProps {
  isOpen: boolean
  onClose: () => void
  type: "add" | "edit"
  option: MappingOption | null
  categoryLabel: string
  onSave: (optionData: Omit<MappingOption, "id" | "createdAt" | "updatedAt">) => void
}

export default function MappingOptionModal({
  isOpen,
  onClose,
  type,
  option,
  categoryLabel,
  onSave,
}: MappingOptionModalProps) {
  const [formData, setFormData] = useState({
    code: "",
    description: "",
    isActive: true,
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (type === "edit" && option) {
      setFormData({
        code: option.code,
        description: option.description,
        isActive: option.isActive,
      })
    } else {
      setFormData({
        code: "",
        description: "",
        isActive: true,
      })
    }
    setErrors({})
  }, [type, option, isOpen])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.code.trim()) {
      newErrors.code = "Code is required"
    } else if (formData.code.length < 2) {
      newErrors.code = "Code must be at least 2 characters"
    } else if (!/^[A-Z0-9_]+$/.test(formData.code)) {
      newErrors.code = "Code must contain only uppercase letters, numbers, and underscores"
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required"
    } else if (formData.description.length < 10) {
      newErrors.description = "Description must be at least 10 characters"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    onSave(formData)
    onClose()
  }

  const handleCodeChange = (value: string) => {
    // Auto-convert to uppercase and replace spaces with underscores
    const formattedCode = value.toUpperCase().replace(/\s+/g, "_")
    setFormData((prev) => ({ ...prev, code: formattedCode }))
  }

  const getModalTitle = () => {
    return type === "add" ? `Add New ${categoryLabel} Option` : `Edit ${categoryLabel} Option`
  }

  const getModalDescription = () => {
    return type === "add"
      ? `Create a new option for the ${categoryLabel} category`
      : `Update the selected ${categoryLabel} option`
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {type === "add" ? <Plus className="w-5 h-5 text-blue-600" /> : <Edit3 className="w-5 h-5 text-blue-600" />}
            <span>{getModalTitle()}</span>
          </DialogTitle>
          <DialogDescription>{getModalDescription()}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            {/* Code Field */}
            <div className="space-y-2">
              <Label htmlFor="code" className="flex items-center space-x-2">
                <Code className="w-4 h-4 text-gray-600" />
                <span>Code</span>
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="code"
                placeholder="Enter option code (e.g., HIGH_PRIORITY)"
                value={formData.code}
                onChange={(e) => handleCodeChange(e.target.value)}
                className={`font-mono ${errors.code ? "border-red-300 focus:border-red-500 focus:ring-red-500" : "border-gray-200 focus:border-blue-500 focus:ring-blue-500"}`}
              />
              {errors.code && <p className="text-sm text-red-600">{errors.code}</p>}
              <p className="text-xs text-gray-500">
                Use uppercase letters, numbers, and underscores only. Spaces will be converted to underscores.
              </p>
            </div>

            {/* Description Field */}
            <div className="space-y-2">
              <Label htmlFor="description" className="flex items-center space-x-2">
                <FileText className="w-4 h-4 text-gray-600" />
                <span>Description</span>
                <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="description"
                placeholder="Enter a detailed description of this option"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                rows={3}
                className={
                  errors.description
                    ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                }
              />
              {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
              <p className="text-xs text-gray-500">
                Provide a clear description of what this option represents and when it should be used.
              </p>
            </div>

            {/* Active Status */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div className="space-y-1">
                <Label htmlFor="isActive" className="text-sm font-medium">
                  Active Status
                </Label>
                <p className="text-xs text-gray-600">
                  {formData.isActive
                    ? "This option will be available for selection"
                    : "This option will be hidden from selection"}
                </p>
              </div>
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, isActive: checked }))}
              />
            </div>
          </div>

          {/* Preview */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Preview</h4>
            <div className="flex items-center space-x-2">
              <code className="px-2 py-1 bg-white rounded text-sm font-mono text-gray-800 border">
                {formData.code || "CODE_PREVIEW"}
              </code>
              <span className="text-sm text-blue-700">{formData.description || "Description will appear here"}</span>
            </div>
          </div>
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            {type === "add" ? "Create Option" : "Update Option"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
