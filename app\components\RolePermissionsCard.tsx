"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Crown, Edit3, Eye, Check, X } from "lucide-react"

const roleData = [
  {
    role: "admin",
    title: "Administrator",
    description: "Full system access with all permissions",
    icon: Crown,
    color: "purple",
    permissions: {
      "Manage Mappings": true,
      "Generate Master Codes": true,
      "View Dashboard": true,
      "Manage Users": true,
      "Export Data": true,
      "Import Data": true,
    },
  },
  {
    role: "editor",
    title: "Editor",
    description: "Can manage mappings and generate codes",
    icon: Edit3,
    color: "blue",
    permissions: {
      "Manage Mappings": true,
      "Generate Master Codes": true,
      "View Dashboard": true,
      "Manage Users": false,
      "Export Data": true,
      "Import Data": false,
    },
  },
  {
    role: "viewer",
    title: "Viewer",
    description: "Read-only access to dashboard and data",
    icon: Eye,
    color: "gray",
    permissions: {
      "Manage Mappings": false,
      "Generate Master Codes": false,
      "View Dashboard": true,
      "Manage Users": false,
      "Export Data": false,
      "Import Data": false,
    },
  },
]

export default function RolePermissionsCard() {
  const getColorClasses = (color: string) => {
    switch (color) {
      case "purple":
        return {
          bg: "bg-purple-100",
          text: "text-purple-800",
          border: "border-purple-200",
          icon: "text-purple-600",
        }
      case "blue":
        return {
          bg: "bg-blue-100",
          text: "text-blue-800",
          border: "border-blue-200",
          icon: "text-blue-600",
        }
      case "gray":
        return {
          bg: "bg-gray-100",
          text: "text-gray-800",
          border: "border-gray-200",
          icon: "text-gray-600",
        }
      default:
        return {
          bg: "bg-gray-100",
          text: "text-gray-800",
          border: "border-gray-200",
          icon: "text-gray-600",
        }
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Role Permissions Overview</h3>
        <p className="text-gray-600">Understanding the different permission levels for each role in the system.</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {roleData.map((roleInfo) => {
          const Icon = roleInfo.icon
          const colors = getColorClasses(roleInfo.color)

          return (
            <Card key={roleInfo.role} className="border-0 shadow-sm">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 ${colors.bg} rounded-xl flex items-center justify-center`}>
                    <Icon className={`w-6 h-6 ${colors.icon}`} />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{roleInfo.title}</CardTitle>
                    <Badge className={`${colors.bg} ${colors.text} ${colors.border} mt-1`}>
                      {roleInfo.role.toUpperCase()}
                    </Badge>
                  </div>
                </div>
                <CardDescription className="mt-2">{roleInfo.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Permissions</h4>
                  <div className="space-y-2">
                    {Object.entries(roleInfo.permissions).map(([permission, hasAccess]) => (
                      <div key={permission} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-700">{permission}</span>
                        <div className="flex items-center">
                          {hasAccess ? (
                            <div className="flex items-center space-x-1 text-green-600">
                              <Check className="w-4 h-4" />
                              <span className="text-xs font-medium">Yes</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-1 text-red-600">
                              <X className="w-4 h-4" />
                              <span className="text-xs font-medium">No</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Permission Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Permission Matrix</CardTitle>
          <CardDescription>Quick reference for all role permissions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left p-3 font-medium text-gray-900">Permission</th>
                  <th className="text-center p-3 font-medium text-purple-600">Admin</th>
                  <th className="text-center p-3 font-medium text-blue-600">Editor</th>
                  <th className="text-center p-3 font-medium text-gray-600">Viewer</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {Object.keys(roleData[0].permissions).map((permission) => (
                  <tr key={permission} className="hover:bg-gray-50">
                    <td className="p-3 font-medium text-gray-900">{permission}</td>
                    {roleData.map((role) => (
                      <td key={role.role} className="p-3 text-center">
                        {role.permissions[permission as keyof typeof role.permissions] ? (
                          <Check className="w-5 h-5 text-green-600 mx-auto" />
                        ) : (
                          <X className="w-5 h-5 text-red-600 mx-auto" />
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
