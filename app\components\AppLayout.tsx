"use client"

import { useState } from "react"
import { SidebarProvider } from "@/components/ui/sidebar"
import { AppSidebar } from "./AppSidebar"
import Dashboard from "./Dashboard"
import MasterCodeGeneration from "./MasterCodeGeneration"
import MappingManagement from "./MappingManagement"
import UserManagement from "./UserManagement"

interface AppLayoutProps {
  onSignOut: () => void
}

export default function AppLayout({ onSignOut }: AppLayoutProps) {
  const [currentPage, setCurrentPage] = useState("dashboard")

  const renderCurrentPage = () => {
    switch (currentPage) {
      case "dashboard":
        return <Dashboard onSignOut={onSignOut} />
      case "generation":
        return <MasterCodeGeneration onNavigate={setCurrentPage} />
      case "mappings":
        return <MappingManagement onNavigate={setCurrentPage} />
      case "users":
        return <UserManagement onNavigate={setCurrentPage} />
      default:
        return <Dashboard onSignOut={onSignOut} />
    }
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <AppSidebar currentPage={currentPage} onNavigate={setCurrentPage} onSignOut={onSignOut} />
        <main className="flex-1">{renderCurrentPage()}</main>
      </div>
    </SidebarProvider>
  )
}
