"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Edit3, UserIcon, Mail, Building, Shield, Settings } from "lucide-react"

interface User {
  id: string
  name: string
  email: string
  role: "admin" | "editor" | "viewer"
  status: "active" | "inactive" | "pending"
  lastLogin: string
  createdAt: string
  department: string
  permissions: {
    canManageMappings: boolean
    canGenerateCodes: boolean
    canViewDashboard: boolean
    canManageUsers: boolean
    canExportData: boolean
    canImportData: boolean
  }
}

interface UserModalProps {
  isOpen: boolean
  onClose: () => void
  type: "add" | "edit"
  user: User | null
  onSave: (userData: Omit<User, "id" | "createdAt" | "lastLogin">) => void
}

const rolePermissions = {
  admin: {
    canManageMappings: true,
    canGenerateCodes: true,
    canViewDashboard: true,
    canManageUsers: true,
    canExportData: true,
    canImportData: true,
  },
  editor: {
    canManageMappings: true,
    canGenerateCodes: true,
    canViewDashboard: true,
    canManageUsers: false,
    canExportData: true,
    canImportData: false,
  },
  viewer: {
    canManageMappings: false,
    canGenerateCodes: false,
    canViewDashboard: true,
    canManageUsers: false,
    canExportData: false,
    canImportData: false,
  },
}

export default function UserModal({ isOpen, onClose, type, user, onSave }: UserModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    role: "viewer" as "admin" | "editor" | "viewer",
    status: "active" as "active" | "inactive" | "pending",
    department: "",
    permissions: {
      canManageMappings: false,
      canGenerateCodes: false,
      canViewDashboard: true,
      canManageUsers: false,
      canExportData: false,
      canImportData: false,
    },
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [customPermissions, setCustomPermissions] = useState(false)

  useEffect(() => {
    if (type === "edit" && user) {
      setFormData({
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        department: user.department,
        permissions: user.permissions,
      })
      // Check if permissions match the role defaults
      const defaultPerms = rolePermissions[user.role]
      const hasCustomPerms = Object.keys(defaultPerms).some(
        (key) =>
          defaultPerms[key as keyof typeof defaultPerms] !== user.permissions[key as keyof typeof user.permissions],
      )
      setCustomPermissions(hasCustomPerms)
    } else {
      setFormData({
        name: "",
        email: "",
        role: "viewer",
        status: "active",
        department: "",
        permissions: rolePermissions.viewer,
      })
      setCustomPermissions(false)
    }
    setErrors({})
  }, [type, user, isOpen])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Name is required"
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    if (!formData.department.trim()) {
      newErrors.department = "Department is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleRoleChange = (newRole: "admin" | "editor" | "viewer") => {
    setFormData((prev) => ({
      ...prev,
      role: newRole,
      permissions: customPermissions ? prev.permissions : rolePermissions[newRole],
    }))
  }

  const handlePermissionChange = (permission: keyof typeof formData.permissions, value: boolean) => {
    setFormData((prev) => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: value,
      },
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    onSave(formData)
  }

  const getModalTitle = () => {
    return type === "add" ? "Add New User" : "Edit User"
  }

  const getModalDescription = () => {
    return type === "add"
      ? "Create a new user account with appropriate permissions"
      : "Update user information and permissions"
  }

  const permissionLabels = {
    canManageMappings: "Manage Mappings",
    canGenerateCodes: "Generate Master Codes",
    canViewDashboard: "View Dashboard",
    canManageUsers: "Manage Users",
    canExportData: "Export Data",
    canImportData: "Import Data",
  }

  const permissionDescriptions = {
    canManageMappings: "Add, edit, and delete mapping options",
    canGenerateCodes: "Generate new master codes",
    canViewDashboard: "Access dashboard and analytics",
    canManageUsers: "Add, edit, and manage user accounts",
    canExportData: "Export codes and data to files",
    canImportData: "Import codes and data from files",
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {type === "add" ? <Plus className="w-5 h-5 text-blue-600" /> : <Edit3 className="w-5 h-5 text-blue-600" />}
            <span>{getModalTitle()}</span>
          </DialogTitle>
          <DialogDescription>{getModalDescription()}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">Basic Information</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="flex items-center space-x-2">
                    <UserIcon className="w-4 h-4 text-gray-600" />
                    <span>Full Name</span>
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    placeholder="Enter full name"
                    value={formData.name}
                    onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                    className={
                      errors.name
                        ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                        : "border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                    }
                  />
                  {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-gray-600" />
                    <span>Email Address</span>
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter email address"
                    value={formData.email}
                    onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                    className={
                      errors.email
                        ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                        : "border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                    }
                  />
                  {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="department" className="flex items-center space-x-2">
                    <Building className="w-4 h-4 text-gray-600" />
                    <span>Department</span>
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="department"
                    placeholder="Enter department"
                    value={formData.department}
                    onChange={(e) => setFormData((prev) => ({ ...prev, department: e.target.value }))}
                    className={
                      errors.department
                        ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                        : "border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                    }
                  />
                  {errors.department && <p className="text-sm text-red-600">{errors.department}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: any) => setFormData((prev) => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="role" className="flex items-center space-x-2">
                  <Shield className="w-4 h-4 text-gray-600" />
                  <span>Role</span>
                </Label>
                <Select value={formData.role} onValueChange={handleRoleChange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin - Full system access</SelectItem>
                    <SelectItem value="editor">Editor - Can manage mappings and generate codes</SelectItem>
                    <SelectItem value="viewer">Viewer - Read-only access</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="permissions" className="space-y-4 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="w-5 h-5 text-blue-600" />
                    <span>Permission Settings</span>
                  </CardTitle>
                  <CardDescription>Configure specific permissions for this user</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div>
                      <p className="font-medium text-blue-900">Use Custom Permissions</p>
                      <p className="text-sm text-blue-700">Override default role permissions with custom settings</p>
                    </div>
                    <Switch
                      checked={customPermissions}
                      onCheckedChange={(checked) => {
                        setCustomPermissions(checked)
                        if (!checked) {
                          setFormData((prev) => ({
                            ...prev,
                            permissions: rolePermissions[prev.role],
                          }))
                        }
                      }}
                    />
                  </div>

                  <div className="space-y-3">
                    {Object.entries(permissionLabels).map(([key, label]) => (
                      <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{label}</p>
                          <p className="text-sm text-gray-600">
                            {permissionDescriptions[key as keyof typeof permissionDescriptions]}
                          </p>
                        </div>
                        <Switch
                          checked={formData.permissions[key as keyof typeof formData.permissions]}
                          onCheckedChange={(checked) =>
                            handlePermissionChange(key as keyof typeof formData.permissions, checked)
                          }
                          disabled={!customPermissions}
                        />
                      </div>
                    ))}
                  </div>

                  {!customPermissions && (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        Permissions are automatically set based on the selected role. Enable custom permissions to
                        override.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
            >
              {type === "add" ? "Create User" : "Update User"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
