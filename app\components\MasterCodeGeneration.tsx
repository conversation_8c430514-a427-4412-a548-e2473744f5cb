"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Zap, Copy, Download, Mail, ArrowRight, CheckCircle, Settings, Code, Layers } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import GenerationResultModal from "./GenerationResultModal"

interface MasterCodeGenerationProps {
  onNavigate: (page: string) => void
}

const dropdownOptions = [
  {
    id: "category",
    label: "Category",
    placeholder: "Select category",
    options: ["Security", "Processing", "Communication", "Reporting", "Integration", "Validation", "Authentication"],
  },
  {
    id: "module",
    label: "Module",
    placeholder: "Select module",
    options: [
      "User Management",
      "Data Processing",
      "Email Service",
      "File Handler",
      "API Gateway",
      "Database",
      "Cache",
    ],
  },
  {
    id: "operation",
    label: "Operation Type",
    placeholder: "Select operation",
    options: ["CREATE", "READ", "UPDATE", "DELETE", "VALIDATE", "PROCESS", "SEND", "RECEIVE"],
  },
  {
    id: "priority",
    label: "Priority Level",
    placeholder: "Select priority",
    options: ["HIGH", "MEDIUM", "LOW", "CRITICAL", "NORMAL"],
  },
  {
    id: "environment",
    label: "Environment",
    placeholder: "Select environment",
    options: ["PROD", "DEV", "TEST", "STAGE", "LOCAL"],
  },
  {
    id: "version",
    label: "Version",
    placeholder: "Select version",
    options: ["V1", "V2", "V3", "BETA", "ALPHA", "STABLE"],
  },
  {
    id: "region",
    label: "Region",
    placeholder: "Select region",
    options: ["US", "EU", "ASIA", "GLOBAL", "LOCAL"],
  },
  {
    id: "protocol",
    label: "Protocol",
    placeholder: "Select protocol",
    options: ["HTTP", "HTTPS", "TCP", "UDP", "WEBSOCKET", "REST", "GRAPHQL"],
  },
  {
    id: "format",
    label: "Data Format",
    placeholder: "Select format",
    options: ["JSON", "XML", "CSV", "BINARY", "TEXT", "YAML"],
  },
  {
    id: "status",
    label: "Initial Status",
    placeholder: "Select status",
    options: ["ACTIVE", "PENDING", "DRAFT", "REVIEW", "APPROVED"],
  },
]

// Add this function at the top of the component to simulate getting data from mappings
const getDropdownOptionsFromMappings = () => {
  // This would normally fetch from the mapping management data
  // For now, keeping the existing static data structure
  return dropdownOptions
}

export default function MasterCodeGeneration({ onNavigate }: MasterCodeGenerationProps) {
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [generatedCode, setGeneratedCode] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [showResultModal, setShowResultModal] = useState(false)

  const handleDropdownChange = (id: string, value: string) => {
    setFormData((prev) => ({ ...prev, [id]: value }))
  }

  const generateMasterCode = async () => {
    const filledFields = Object.keys(formData).filter((key) => formData[key])

    if (filledFields.length < 5) {
      toast({
        title: "Insufficient Information",
        description: "Please fill at least 5 fields to generate a master code.",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)

    // Simulate code generation
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Generate code based on selections
    const codeComponents = [
      formData.category?.substring(0, 3).toUpperCase(),
      formData.module?.replace(/\s+/g, "").substring(0, 4).toUpperCase(),
      formData.operation?.substring(0, 3),
      formData.priority?.substring(0, 1),
      formData.environment?.substring(0, 3),
      Date.now().toString().slice(-4),
    ].filter(Boolean)

    const code = codeComponents.join("_")
    setGeneratedCode(code)
    setIsGenerating(false)
    setShowResultModal(true)
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedCode)
    toast({
      title: "Copied!",
      description: "Master code copied to clipboard.",
    })
  }

  const getCompletionPercentage = () => {
    const filledFields = Object.keys(formData).filter((key) => formData[key])
    return Math.round((filledFields.length / dropdownOptions.length) * 100)
  }

  const filledCount = Object.keys(formData).filter((key) => formData[key]).length

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Master Code Generation</h1>
                  <p className="text-sm text-gray-600">
                    Generate custom master codes with intelligent configuration (Options managed in Mappings)
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                {filledCount}/{dropdownOptions.length} Fields
              </Badge>
              <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300"
                  style={{ width: `${getCompletionPercentage()}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="p-6 max-w-7xl mx-auto space-y-6">
        {/* Progress Card */}
        <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-sm">
                  <Settings className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Configuration Progress</h3>
                  <p className="text-sm text-gray-600">
                    {filledCount === 0
                      ? "Start by selecting your code parameters"
                      : filledCount < 5
                        ? `${5 - filledCount} more fields needed to generate`
                        : "Ready to generate your master code!"}
                  </p>
                </div>
              </div>
              {filledCount >= 5 && (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">Ready</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Configuration Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Code className="w-5 h-5 text-blue-600" />
              <span>Code Configuration</span>
            </CardTitle>
            <CardDescription>
              Configure the parameters for your master code generation. Fill at least 5 fields to proceed.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dropdownOptions.map((dropdown, index) => (
                <div key={dropdown.id} className="space-y-2">
                  <Label htmlFor={dropdown.id} className="text-sm font-medium text-gray-700">
                    {dropdown.label}
                    {formData[dropdown.id] && <CheckCircle className="inline w-4 h-4 ml-2 text-green-500" />}
                  </Label>
                  <Select
                    value={formData[dropdown.id] || ""}
                    onValueChange={(value) => handleDropdownChange(dropdown.id, value)}
                  >
                    <SelectTrigger className="h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue placeholder={dropdown.placeholder} />
                    </SelectTrigger>
                    <SelectContent>
                      {dropdown.options.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))}
            </div>

            {/* Preview Section */}
            {filledCount > 0 && (
              <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                  <Layers className="w-4 h-4 mr-2 text-gray-600" />
                  Selected Configuration
                </h4>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(formData)
                    .filter(([_, value]) => value)
                    .map(([key, value]) => (
                      <Badge key={key} variant="secondary" className="bg-blue-100 text-blue-800">
                        {dropdownOptions.find((d) => d.id === key)?.label}: {value}
                      </Badge>
                    ))}
                </div>
              </div>
            )}

            {/* Generate Button */}
            <div className="flex justify-center pt-6">
              <Button
                onClick={generateMasterCode}
                disabled={filledCount < 5 || isGenerating}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="w-5 h-5 mr-2" />
                    Generate Master Code
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Generated Code Display */}
        {generatedCode && (
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-green-800">
                <CheckCircle className="w-5 h-5" />
                <span>Generated Master Code</span>
              </CardTitle>
              <CardDescription className="text-green-700">
                Your master code has been successfully generated based on your configuration.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-green-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Code className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Master Code</p>
                    <p className="text-xl font-mono font-bold text-gray-900">{generatedCode}</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  className="border-green-200 text-green-700 hover:bg-green-50"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy
                </Button>
              </div>

              <div className="flex flex-wrap gap-3 justify-center">
                <Button onClick={() => setShowResultModal(true)} className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Mail className="w-4 h-4 mr-2" />
                  Email Code
                </Button>
                <Button
                  onClick={() => setShowResultModal(true)}
                  variant="outline"
                  className="border-blue-200 text-blue-700 hover:bg-blue-50"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export Code
                </Button>
                <Button
                  onClick={() => onNavigate("dashboard")}
                  variant="outline"
                  className="border-gray-200 text-gray-700 hover:bg-gray-50"
                >
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Go to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Result Modal */}
      <GenerationResultModal
        isOpen={showResultModal}
        onClose={() => setShowResultModal(false)}
        generatedCode={generatedCode}
        onNavigate={onNavigate}
      />
    </div>
  )
}
