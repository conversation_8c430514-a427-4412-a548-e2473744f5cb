"use client"

import { useState } from "react"
import { Menu } from "lucide-react"
import { Button } from "@/components/ui/button"

interface SidebarTriggerProps {
  className?: string
}

export function SidebarTrigger({ className }: SidebarTriggerProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <Button variant="ghost" size="sm" className={className} onClick={() => setIsOpen(!isOpen)}>
      <Menu className="w-5 h-5" />
    </Button>
  )
}
