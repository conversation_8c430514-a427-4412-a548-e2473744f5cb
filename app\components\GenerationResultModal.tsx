"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Mail, Download, ArrowRight, Send, FileDown, CheckCircle } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface GenerationResultModalProps {
  isOpen: boolean
  onClose: () => void
  generatedCode: string
  onNavigate: (page: string) => void
}

export default function GenerationResultModal({
  isOpen,
  onClose,
  generatedCode,
  onNavigate,
}: GenerationResultModalProps) {
  const [emailData, setEmailData] = useState({
    to: "",
    subject: `Master Code Generated: ${generatedCode}`,
    message: `Your master code has been generated successfully.\n\nGenerated Code: ${generatedCode}\n\nThis code has been created based on your specified configuration parameters.`,
  })
  const [isProcessing, setIsProcessing] = useState(false)

  const handleEmailSend = async () => {
    if (!emailData.to) {
      toast({
        title: "Email Required",
        description: "Please enter an email address.",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    // Simulate email sending
    await new Promise((resolve) => setTimeout(resolve, 2000))

    toast({
      title: "Email Sent!",
      description: `Master code sent to ${emailData.to}`,
    })

    setIsProcessing(false)
    onClose()
  }

  const handleExport = async () => {
    setIsProcessing(true)

    // Simulate export process
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // Create and download a simple text file
    const content = `Master Code: ${generatedCode}\nGenerated: ${new Date().toLocaleString()}\n\nConfiguration Details:\nThis master code was generated using the Code Management System.`
    const blob = new Blob([content], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `master-code-${generatedCode}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "Export Complete!",
      description: "Master code exported successfully.",
    })

    setIsProcessing(false)
    onClose()
  }

  const handleNavigateToDashboard = () => {
    onNavigate("dashboard")
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span>Master Code Generated</span>
          </DialogTitle>
          <DialogDescription>
            Your master code <strong>{generatedCode}</strong> has been generated successfully. Choose how you'd like to
            proceed.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="email" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="email" className="flex items-center space-x-2">
              <Mail className="w-4 h-4" />
              <span>Email</span>
            </TabsTrigger>
            <TabsTrigger value="export" className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export</span>
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center space-x-2">
              <ArrowRight className="w-4 h-4" />
              <span>Dashboard</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="email" className="space-y-4 mt-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email-to">Email Address</Label>
                <Input
                  id="email-to"
                  type="email"
                  placeholder="Enter recipient email"
                  value={emailData.to}
                  onChange={(e) => setEmailData((prev) => ({ ...prev, to: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email-subject">Subject</Label>
                <Input
                  id="email-subject"
                  value={emailData.subject}
                  onChange={(e) => setEmailData((prev) => ({ ...prev, subject: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email-message">Message</Label>
                <Textarea
                  id="email-message"
                  rows={4}
                  value={emailData.message}
                  onChange={(e) => setEmailData((prev) => ({ ...prev, message: e.target.value }))}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={handleEmailSend} disabled={isProcessing} className="bg-blue-600 hover:bg-blue-700">
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Send Email
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="export" className="space-y-4 mt-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <FileDown className="w-8 h-8 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Export Master Code</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Download your generated master code as a text file with all configuration details.
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">File will include:</p>
                <ul className="text-sm text-gray-800 mt-2 space-y-1">
                  <li>• Master Code: {generatedCode}</li>
                  <li>• Generation timestamp</li>
                  <li>• Configuration parameters</li>
                </ul>
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={handleExport} disabled={isProcessing} className="bg-green-600 hover:bg-green-700">
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    Download File
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="dashboard" className="space-y-4 mt-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <ArrowRight className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Go to Dashboard</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Return to the main dashboard to view your generated code in the master codes list and continue
                  managing your codes.
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-800">
                  Your generated code <strong>{generatedCode}</strong> will be automatically added to your master codes
                  collection.
                </p>
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={onClose}>
                Stay Here
              </Button>
              <Button onClick={handleNavigateToDashboard} className="bg-blue-600 hover:bg-blue-700">
                <ArrowRight className="w-4 h-4 mr-2" />
                Go to Dashboard
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
