"use client"

import { BarChart3, Code2, FileText, LogOut, TrendingUp, Users, Zap, Home } from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"

interface AppSidebarProps {
  currentPage: string
  onNavigate: (page: string) => void
  onSignOut: () => void
}

// Mock user data - in real app this would come from auth context
const currentUser = {
  name: "<PERSON>",
  email: "<EMAIL>",
  role: "admin",
  avatar: null,
  department: "IT Administration",
}

const navigationItems = [
  {
    title: "Main",
    items: [
      {
        title: "Dashboard",
        url: "dashboard",
        icon: Home,
        badge: null,
      },
      {
        title: "Master Codes",
        url: "codes",
        icon: FileText,
        badge: "2.8k",
      },
      {
        title: "Code Generation",
        url: "generation",
        icon: Zap,
        badge: null,
      },
    ],
  },
  {
    title: "Manage",
    items: [
      {
        title: "Mappings",
        url: "mappings",
        icon: TrendingUp,
        badge: null,
      },
      {
        title: "Users",
        url: "users",
        icon: Users,
        badge: null,
      },
      {
        title: "Reports",
        url: "reports",
        icon: BarChart3,
        badge: null,
      },
    ],
  },
]

const getRoleColor = (role: string) => {
  switch (role) {
    case "admin":
      return "bg-blue-50 text-blue-700 border-blue-200"
    case "editor":
      return "bg-green-50 text-green-700 border-green-200"
    case "viewer":
      return "bg-gray-50 text-gray-700 border-gray-200"
    default:
      return "bg-gray-50 text-gray-700 border-gray-200"
  }
}

export function AppSidebar({ currentPage, onNavigate, onSignOut }: AppSidebarProps) {
  return (
    <Sidebar className="border-r border-gray-200 bg-white">
      <SidebarHeader className="border-b border-gray-100 px-6 py-6">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <Code2 className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">CodeManager</h2>
            <p className="text-xs text-gray-500">v2.1.0</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-4 py-6">
        {navigationItems.map((group, groupIndex) => (
          <SidebarGroup key={group.title} className={groupIndex > 0 ? "mt-8" : ""}>
            <SidebarGroupLabel className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3 px-2">
              {group.title}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {group.items.map((item) => {
                  const Icon = item.icon
                  const isActive = currentPage === item.url

                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        onClick={() => onNavigate(item.url)}
                        className={`group relative w-full px-3 py-2.5 rounded-lg transition-all duration-150 ${
                          isActive ? "bg-blue-600 text-white" : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                        }`}
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center space-x-3">
                            <Icon className={`w-5 h-5 ${isActive ? "text-white" : "text-gray-500"}`} />
                            <span className="font-medium text-sm">{item.title}</span>
                          </div>
                          {item.badge && (
                            <Badge
                              variant="secondary"
                              className={`text-xs px-2 py-0.5 ${
                                isActive
                                  ? "bg-blue-500 text-blue-100 border-blue-400"
                                  : "bg-gray-100 text-gray-600 border-gray-200"
                              }`}
                            >
                              {item.badge}
                            </Badge>
                          )}
                        </div>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter className="border-t border-gray-100 p-4">
        {/* User Profile Section */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <Avatar className="w-9 h-9">
              <AvatarFallback className="bg-blue-600 text-white text-sm font-medium">
                {currentUser.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-sm text-gray-900 truncate">{currentUser.name}</p>
              <div className="flex items-center space-x-2 mt-0.5">
                <Badge className={`text-xs px-1.5 py-0.5 ${getRoleColor(currentUser.role)}`}>{currentUser.role}</Badge>
                <span className="text-xs text-gray-500 truncate">{currentUser.department}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Sign Out Button */}
        <SidebarMenu>
          <SidebarMenuItem>
            <Button
              variant="ghost"
              onClick={onSignOut}
              className="w-full justify-start text-gray-600 hover:text-gray-900 hover:bg-gray-100 px-3 py-2"
            >
              <LogOut className="w-4 h-4 mr-3" />
              <span className="text-sm font-medium">Sign out</span>
            </Button>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  )
}
