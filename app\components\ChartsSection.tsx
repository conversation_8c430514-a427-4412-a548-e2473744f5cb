"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Line<PERSON>hart,
  Line,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts"

const barData = [
  { month: "Jan", codes: 245, mappings: 189 },
  { month: "Feb", codes: 289, mappings: 234 },
  { month: "Mar", codes: 312, mappings: 267 },
  { month: "Apr", codes: 298, mappings: 289 },
  { month: "May", codes: 334, mappings: 312 },
  { month: "Jun", codes: 367, mappings: 334 },
]

const lineData = [
  { day: "Mon", requests: 45 },
  { day: "Tue", requests: 52 },
  { day: "Wed", requests: 48 },
  { day: "Thu", requests: 61 },
  { day: "Fri", requests: 55 },
  { day: "Sat", requests: 32 },
  { day: "Sun", requests: 28 },
]

const pieData = [
  { name: "Active", value: 65, color: "#3b82f6" },
  { name: "Pending", value: 25, color: "#f59e0b" },
  { name: "Inactive", value: 10, color: "#ef4444" },
]

export default function ChartsSection() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Bar Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Code Activity</CardTitle>
          <CardDescription>Master codes and mappings created per month</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              codes: {
                label: "Master Codes",
                color: "#3b82f6",
              },
              mappings: {
                label: "Mappings",
                color: "#6366f1",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="month" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="codes" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                <Bar dataKey="mappings" fill="#6366f1" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Line Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Weekly Requests</CardTitle>
          <CardDescription>Dashboard requests over the past week</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              requests: {
                label: "Requests",
                color: "#10b981",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={lineData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="day" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line
                  type="monotone"
                  dataKey="requests"
                  stroke="#10b981"
                  strokeWidth={3}
                  dot={{ fill: "#10b981", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Pie Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Code Status Distribution</CardTitle>
          <CardDescription>Current status of all master codes</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              active: {
                label: "Active",
                color: "#3b82f6",
              },
              pending: {
                label: "Pending",
                color: "#f59e0b",
              },
              inactive: {
                label: "Inactive",
                color: "#ef4444",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <ChartTooltip content={<ChartTooltipContent />} />
              </PieChart>
            </ResponsiveContainer>
          </ChartContainer>
          <div className="flex justify-center space-x-6 mt-4">
            {pieData.map((entry, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
                <span className="text-sm text-gray-600">
                  {entry.name}: {entry.value}%
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Usage Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Trends</CardTitle>
          <CardDescription>System usage patterns and trends</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div>
                <p className="font-medium text-blue-900">Peak Usage Time</p>
                <p className="text-sm text-blue-700">2:00 PM - 4:00 PM</p>
              </div>
              <div className="text-2xl font-bold text-blue-600">+23%</div>
            </div>
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
              <div>
                <p className="font-medium text-green-900">Most Active Day</p>
                <p className="text-sm text-green-700">Thursday</p>
              </div>
              <div className="text-2xl font-bold text-green-600">+18%</div>
            </div>
            <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
              <div>
                <p className="font-medium text-purple-900">Average Session</p>
                <p className="text-sm text-purple-700">12 minutes</p>
              </div>
              <div className="text-2xl font-bold text-purple-600">+5%</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
